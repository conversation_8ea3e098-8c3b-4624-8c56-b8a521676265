{"type": "extension", "name": "coze_python_async", "version": "0.1.0", "dependencies": [{"type": "system", "name": "ten_runtime_python", "version": "0.8"}], "package": {"include": ["manifest.json", "property.json", "BUILD.gn", "**.tent", "**.py", "README.md", "tests/**"]}, "api": {"property": {"base_url": {"type": "string"}, "bot_id": {"type": "string"}, "token": {"type": "string"}, "user_id": {"type": "string"}, "prompt": {"type": "string"}, "greeting": {"type": "string"}}, "data_in": [{"name": "text_data", "property": {"text": {"type": "string"}}}], "data_out": [{"name": "text_data", "property": {"text": {"type": "string"}}}], "cmd_in": [{"name": "flush"}], "cmd_out": [{"name": "flush"}]}}