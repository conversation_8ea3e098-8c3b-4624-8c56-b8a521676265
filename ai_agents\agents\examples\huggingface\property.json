{"_ten": {"predefined_graphs": [{"name": "with_ten_turn_detection", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": true, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY|}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION|}", "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "llm", "addon": "openai_chatgpt_python", "extension_group": "chatgpt", "property": {"api_key": "${env:OPENAI_API_KEY}", "base_url": "${env:OPENAI_API_BASE|}", "frequency_penalty": 0.9, "greeting": "TEN Agent connected. How can I help you today?", "max_memory_length": 10, "max_tokens": 512, "model": "${env:OPENAI_MODEL}", "prompt": "", "proxy_url": "${env:OPENAI_PROXY_URL|}"}}, {"type": "extension", "name": "tts", "addon": "azure_tts", "extension_group": "tts", "property": {"azure_subscription_key": "${env:AZURE_TTS_KEY}", "azure_subscription_region": "${env:AZURE_TTS_REGION}", "azure_synthesis_voice_name": "en-US-AndrewMultilingualNeural"}}, {"type": "extension", "name": "interrupt_detector", "addon": "ten_turn_detection", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}, {"name": "content_data", "dest": [{"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}]}, {"name": "without_ten_turn_detection_and_ten_vad", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": true, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY|}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION|}", "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "llm", "addon": "openai_chatgpt_python", "extension_group": "chatgpt", "property": {"api_key": "${env:OPENAI_API_KEY}", "base_url": "${env:OPENAI_API_BASE|}", "frequency_penalty": 0.9, "greeting": "TEN Agent connected. How can I help you today?", "max_memory_length": 10, "max_tokens": 512, "model": "${env:OPENAI_MODEL}", "prompt": "", "proxy_url": "${env:OPENAI_PROXY_URL|}"}}, {"type": "extension", "name": "tts", "addon": "azure_tts", "extension_group": "tts", "property": {"azure_subscription_key": "${env:AZURE_TTS_KEY}", "azure_subscription_region": "${env:AZURE_TTS_REGION}", "azure_synthesis_voice_name": "en-US-AndrewMultilingualNeural"}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}, {"name": "content_data", "dest": [{"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}]}, {"name": "with_ten_turn_detection_and_ten_vad", "auto_start": false, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": true, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY|}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION|}", "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "llm", "addon": "openai_chatgpt_python", "extension_group": "chatgpt", "property": {"api_key": "${env:OPENAI_API_KEY}", "base_url": "${env:OPENAI_API_BASE|}", "frequency_penalty": 0.9, "greeting": "TEN Agent connected. How can I help you today?", "max_memory_length": 10, "max_tokens": 512, "model": "${env:OPENAI_MODEL}", "prompt": "", "proxy_url": "${env:OPENAI_PROXY_URL|}"}}, {"type": "extension", "name": "tts", "addon": "azure_tts", "extension_group": "tts", "property": {"azure_subscription_key": "${env:AZURE_TTS_KEY}", "azure_subscription_region": "${env:AZURE_TTS_REGION}", "azure_synthesis_voice_name": "en-US-AndrewMultilingualNeural"}}, {"type": "extension", "name": "interrupt_detector", "addon": "ten_turn_detection", "extension_group": "default", "property": {}}, {"type": "extension", "name": "vad", "addon": "ten_vad_python", "extension_group": "vad", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "vad"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}, {"name": "content_data", "dest": [{"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}, {"extension": "vad", "cmd": [{"name": "start_of_sentence", "dest": [{"extension": "llm", "msg_conversion": {"type": "per_property", "keep_original": true, "rules": [{"path": "_ten.name", "conversion_mode": "fixed_value", "value": "flush"}]}}]}]}]}], "log_level": 3}}