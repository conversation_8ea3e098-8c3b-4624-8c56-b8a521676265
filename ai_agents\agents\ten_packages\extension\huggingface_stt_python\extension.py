from ten import (
    AsyncExtension,
    AsyncTenEnv,
    Cmd,
    Data,
    AudioFrame,
    StatusCode,
    CmdResult,
)

import asyncio
import numpy as np
import torch
from faster_whisper import WhisperModel
import webrtcvad
from dataclasses import dataclass
from ten_ai_base.config import BaseConfig
import logging
from collections import deque
import time

DATA_OUT_TEXT_DATA_PROPERTY_TEXT = "text"
DATA_OUT_TEXT_DATA_PROPERTY_IS_FINAL = "is_final"
DATA_OUT_TEXT_DATA_PROPERTY_STREAM_ID = "stream_id"
DATA_OUT_TEXT_DATA_PROPERTY_END_OF_SEGMENT = "end_of_segment"
DATA_OUT_TEXT_DATA_PROPERTY_CONFIDENCE = "confidence"

@dataclass
class HuggingFaceSTTConfig(BaseConfig):
    model_id: str = "base"  # Faster whisper model size: tiny, base, small, medium, large
    language: str = "en"
    sample_rate: int = 16000
    device: str = "auto"  # auto, cpu, cuda
    vad_enabled: bool = True
    chunk_length_s: int = 2  # Process every 2 seconds for speed
    vad_aggressiveness: int = 2  # 0-3, higher = more aggressive
    min_speech_duration: float = 0.3  # Minimum speech duration to process

class HuggingFaceSTTExtension(AsyncExtension):
    def __init__(self, name: str):
        super().__init__(name)
        self.config: HuggingFaceSTTConfig = None
        self.ten_env: AsyncTenEnv = None
        self.loop = None
        self.stream_id = -1
        self.model = None
        self.vad = None
        self.audio_buffer = deque(maxlen=48000)  # 3 seconds at 16kHz
        self.speech_buffer = []
        self.is_processing = False
        self.last_speech_time = 0
        self.processing_queue = asyncio.Queue()

    async def on_init(self, ten_env: AsyncTenEnv) -> None:
        ten_env.log_info("HuggingFaceSTTExtension on_init")

    async def on_start(self, ten_env: AsyncTenEnv) -> None:
        ten_env.log_info("HuggingFaceSTT on_start - Optimized for speed & accuracy")
        self.loop = asyncio.get_event_loop()
        self.ten_env = ten_env

        self.config = await HuggingFaceSTTConfig.create_async(ten_env=ten_env)
        ten_env.log_info(f"HuggingFaceSTT config: {self.config}")

        # Initialize the model
        await self._initialize_model()

        # Start processing worker
        asyncio.create_task(self._audio_processor())

        ten_env.log_info("HuggingFaceSTT started successfully")

    async def _initialize_model(self):
        """Initialize the Faster Whisper model for optimal performance"""
        try:
            self.ten_env.log_info(f"Loading Faster Whisper model: {self.config.model_id}")

            # Determine device
            device = self.config.device
            if device == "auto":
                if torch.cuda.is_available():
                    device = "cuda"
                    compute_type = "float16"  # Faster on GPU
                else:
                    device = "cpu"
                    compute_type = "int8"    # Faster on CPU
            else:
                compute_type = "float16" if device == "cuda" else "int8"

            self.ten_env.log_info(f"Using device: {device}, compute_type: {compute_type}")

            # Initialize Faster Whisper model (much faster than transformers)
            self.model = WhisperModel(
                self.config.model_id,
                device=device,
                compute_type=compute_type,
                cpu_threads=4,  # Optimize for performance
                num_workers=1   # Single worker for real-time
            )

            # Initialize VAD for speech detection
            if self.config.vad_enabled:
                self.vad = webrtcvad.Vad(self.config.vad_aggressiveness)
                self.ten_env.log_info("VAD enabled for speech detection")

            self.ten_env.log_info("Faster Whisper model loaded successfully")

        except Exception as e:
            self.ten_env.log_error(f"Failed to load model: {e}")
            raise

    async def on_audio_frame(self, _: AsyncTenEnv, frame: AudioFrame) -> None:
        frame_buf = frame.get_buf()

        if not frame_buf or not self.model:
            return

        self.stream_id = frame.get_property_int("stream_id")

        try:
            # Convert bytes to numpy array (16-bit PCM)
            audio_data = np.frombuffer(frame_buf, dtype=np.int16)

            # Add to circular buffer
            self.audio_buffer.extend(audio_data)

            # VAD check for speech activity
            if self.config.vad_enabled and self.vad:
                # Convert to bytes for VAD (needs 10, 20, or 30ms frames)
                frame_duration_ms = 20  # 20ms frame
                frame_size = int(self.config.sample_rate * frame_duration_ms / 1000)

                if len(audio_data) >= frame_size:
                    vad_frame = audio_data[:frame_size].tobytes()
                    is_speech = self.vad.is_speech(vad_frame, self.config.sample_rate)

                    if is_speech:
                        self.last_speech_time = time.time()
                        self.speech_buffer.extend(audio_data)
                    elif self.speech_buffer and time.time() - self.last_speech_time > 0.5:
                        # End of speech detected, queue for processing
                        if len(self.speech_buffer) > self.config.sample_rate * self.config.min_speech_duration:
                            await self.processing_queue.put(np.array(self.speech_buffer, dtype=np.float32) / 32768.0)
                        self.speech_buffer = []
            else:
                # No VAD - process chunks periodically
                if len(self.audio_buffer) >= self.config.sample_rate * self.config.chunk_length_s:
                    chunk = np.array(list(self.audio_buffer)[-self.config.sample_rate * self.config.chunk_length_s:], dtype=np.float32) / 32768.0
                    await self.processing_queue.put(chunk)

        except Exception as e:
            self.ten_env.log_error(f"Error processing audio frame: {e}")

    async def _audio_processor(self):
        """Background worker for processing audio chunks"""
        while True:
            try:
                # Get audio chunk from queue
                audio_chunk = await self.processing_queue.get()

                if len(audio_chunk) == 0:
                    continue

                # Run inference in executor for non-blocking operation
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None,
                    self._run_faster_whisper_inference,
                    audio_chunk
                )

                if result:
                    text, confidence = result
                    if text and text.strip():
                        self.ten_env.log_info(f"Transcribed: '{text}' (confidence: {confidence:.2f})")

                        # Send high-confidence results immediately
                        await self._send_text(
                            text=text.strip(),
                            is_final=True,
                            stream_id=self.stream_id,
                            confidence=confidence
                        )

            except Exception as e:
                self.ten_env.log_error(f"Error in audio processor: {e}")

    def _run_faster_whisper_inference(self, audio_array):
        """Run Faster Whisper inference"""
        try:
            if len(audio_array) == 0:
                return None

            # Transcribe with Faster Whisper
            segments, info = self.model.transcribe(
                audio_array,
                language=self.config.language,
                beam_size=1,  # Faster beam search
                best_of=1,    # Single candidate for speed
                temperature=0,  # Deterministic for accuracy
                vad_filter=False,  # We handle VAD ourselves
                vad_parameters=None
            )

            text_parts = []
            total_confidence = 0
            segment_count = 0

            for segment in segments:
                text_parts.append(segment.text)
                total_confidence += segment.avg_logprob
                segment_count += 1

            if text_parts:
                full_text = " ".join(text_parts)
                avg_confidence = total_confidence / segment_count if segment_count > 0 else 0
                # Convert log probability to confidence score
                confidence = min(max((avg_confidence + 1) * 50, 0), 100) / 100

                return full_text, confidence

            return None

        except Exception as e:
            self.ten_env.log_error(f"Faster Whisper inference error: {e}")
            return None

    async def _send_text(self, text: str, is_final: bool, stream_id: int, confidence: float = 1.0) -> None:
        """Send transcribed text as TEN data"""
        stable_data = Data.create("text_data")
        stable_data.set_property_bool(DATA_OUT_TEXT_DATA_PROPERTY_IS_FINAL, is_final)
        stable_data.set_property_string(DATA_OUT_TEXT_DATA_PROPERTY_TEXT, text)
        stable_data.set_property_int(DATA_OUT_TEXT_DATA_PROPERTY_STREAM_ID, stream_id)
        stable_data.set_property_bool(DATA_OUT_TEXT_DATA_PROPERTY_END_OF_SEGMENT, is_final)
        stable_data.set_property_float(DATA_OUT_TEXT_DATA_PROPERTY_CONFIDENCE, confidence)

        asyncio.create_task(self.ten_env.send_data(stable_data))

    async def on_stop(self, ten_env: AsyncTenEnv) -> None:
        ten_env.log_info("HuggingFaceSTT on_stop")

    async def on_cmd(self, ten_env: AsyncTenEnv, cmd: Cmd) -> None:
        cmd_json = cmd.to_json()
        ten_env.log_info(f"HuggingFaceSTT on_cmd: {cmd_json}")

        cmd_result = CmdResult.create(StatusCode.OK)
        cmd_result.set_property_string("detail", "success")
        await ten_env.return_result(cmd_result, cmd)