name: Designer Feedback(FE)
description: Submit feedback, report issues, or request features
title: "[Feedback]: "
labels: ["tman designer"]
assignees:
  - shczhen
body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking time to provide feedback! Please fill out this form as detailed as possible.

  - type: dropdown
    id: feedback-type
    attributes:
      label: Feedback Type
      description: What kind of feedback are you providing?
      options:
        - Bug Report
        - Feature Request
        - Improvement Suggestion
        - Question
        - Other
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Description
      description: Please describe your feedback in detail
      placeholder: Provide a clear and detailed explanation of your feedback...
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: What did you expect to happen?
      placeholder: Describe what you expected...

  - type: textarea
    id: current
    attributes:
      label: Current Behavior
      description: What actually happened?
      placeholder: Describe the current behavior...

  - type: input
    id: version
    attributes:
      label: Version
      description: What version of the software are you using?
      placeholder: e.g., v1.0.0

  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this feedback, you agree to follow our Code of Conduct
      options:
        - label: I agree to follow this project's Code of Conduct
          required: true