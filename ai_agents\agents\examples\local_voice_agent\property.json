{"_ten": {"predefined_graphs": [{"name": "local_voice_assistant", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "app_certificate": "${env:AGORA_APP_CERTIFICATE|}", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": false}}, {"type": "extension", "name": "stt", "addon": "huggingface_stt_python", "extension_group": "stt", "property": {"model_id": "base", "language": "en", "sample_rate": 16000, "device": "auto", "vad_enabled": true, "chunk_length_s": 2, "vad_aggressiveness": 2, "min_speech_duration": 0.3}}, {"type": "extension", "name": "llm", "addon": "olla<PERSON>_qwen_python", "extension_group": "llm", "property": {"model": "qwen2.5-vl", "base_url": "http://localhost:11434", "temperature": 0.8, "max_tokens": 2048, "system_prompt": "You are a helpful AI assistant with vision capabilities. Respond naturally and conversationally. Be concise but informative. If you can see an image, describe what you observe and relate it to the conversation.", "greeting": "Hello! I'm your fully local AI assistant powered by <PERSON><PERSON> 2.5V<PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> TTS. I can see, hear, and speak - all running locally on your machine for maximum privacy and speed. How can I help you today?", "stream_mode": true}}, {"type": "extension", "name": "tts", "addon": "huggingface_tts_python", "extension_group": "tts", "property": {"model_id": "edge-tts", "voice_preset": "female_natural", "sample_rate": 24000, "device": "auto", "speed": 1.1, "stability": 0.85}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "stt"}]}]}, {"extension": "stt", "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}, {"name": "content_data", "dest": [{"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}]}], "log_level": 3}}