{"type": "extension", "name": "bingsearch_tool_python", "version": "0.1.0", "dependencies": [{"type": "system", "name": "ten_runtime_python", "version": "0.8"}], "package": {"include": ["manifest.json", "property.json", "BUILD.gn", "**.tent", "**.py", "README.md"]}, "api": {"property": {"api_key": {"type": "string"}}, "cmd_out": [{"name": "tool_register", "property": {"tool": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "parameters": {"type": "array", "items": {"type": "object", "properties": {}}}}, "required": ["name", "description", "parameters"]}}, "result": {"property": {"response": {"type": "string"}}}}], "cmd_in": [{"name": "tool_call", "property": {"name": {"type": "string"}, "args": {"type": "string"}}, "required": ["name"]}]}}