{"type": "extension", "name": "huggingface_stt_python", "version": "0.1.0", "dependencies": [{"type": "system", "name": "ten_runtime_python", "version": "0.8"}], "api": {"property": {"model_id": {"type": "string"}, "language": {"type": "string"}, "sample_rate": {"type": "int64"}, "device": {"type": "string"}, "vad_enabled": {"type": "bool"}, "chunk_length_s": {"type": "int64"}}, "audio_frame_in": [{"name": "pcm_frame", "property": {}}], "cmd_in": [{"name": "on_user_joined", "property": {"user_id": {"type": "string"}}}, {"name": "on_user_left", "property": {"user_id": {"type": "string"}}}], "data_out": [{"name": "text_data", "property": {"text": {"type": "string"}, "is_final": {"type": "bool"}, "stream_id": {"type": "uint32"}, "end_of_segment": {"type": "bool"}, "confidence": {"type": "float64"}}}]}}