{"type": "extension", "name": "litellm_python", "version": "0.1.0", "dependencies": [{"type": "system", "name": "ten_runtime_python", "version": "0.8"}], "api": {"property": {"api_key": {"type": "string"}, "base_url": {"type": "string"}, "frequency_penalty": {"type": "float64"}, "greeting": {"type": "string"}, "max_memory_length": {"type": "int64"}, "max_tokens": {"type": "int64"}, "model": {"type": "string"}, "presence_penalty": {"type": "float64"}, "prompt": {"type": "string"}, "provider": {"type": "string"}, "temperature": {"type": "float64"}, "top_p": {"type": "float64"}}, "data_in": [{"name": "text_data", "property": {"text": {"type": "string"}}}], "data_out": [{"name": "text_data", "property": {"text": {"type": "string"}}}], "cmd_in": [{"name": "flush"}], "cmd_out": [{"name": "flush"}]}}