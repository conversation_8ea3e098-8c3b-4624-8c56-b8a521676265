@echo off
echo ========================================
echo 🔍 CHECKING SYSTEM REQUIREMENTS
echo ========================================
echo.

echo Checking Go installation...
go version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Go is installed!
    go version
    echo.
    echo 🚀 Starting Local Voice Agent...
    echo.
    
    echo 🦙 Checking Ollama...
    ollama list | findstr "qwen2.5vl"
    if %errorlevel% == 0 (
        echo ✅ Qwen 2.5VL model found!
    ) else (
        echo ⚠️ Qwen 2.5VL model not found. Pulling it now...
        ollama pull qwen2.5vl:32b
    )
    echo.
    
    echo 🏗️ Building and starting the agent...
    cd ai_agents\agents
    
    echo Setting up environment...
    set PYTHONPATH=%cd%\ten_packages\system\ten_ai_base\interface;%PYTHONPATH%
    
    echo Building Go application...
    go mod tidy
    go build -o bin\worker main.go
    
    if exist bin\worker.exe (
        echo ✅ Build successful! Starting agent...
        echo.
        echo 🌐 Web interface will be available at:
        echo    http://localhost:3000
        echo.
        echo 🎯 Select: "🏠 Local Voice Agent - Fully Offline"
        echo.
        bin\worker.exe
    ) else (
        echo ❌ Build failed. Please check for errors above.
        pause
    )
    
) else (
    echo ❌ Go is not installed or not in PATH
    echo.
    echo Please install Go first:
    echo 1. Download from: https://golang.org/dl/
    echo 2. Or use: winget install golang.go
    echo 3. Restart terminal after installation
    echo.
    echo Opening Go download page...
    start https://golang.org/dl/
)

echo.
pause
