{"type": "extension", "name": "cosy_tts_python", "version": "0.1.0", "dependencies": [{"type": "system", "name": "ten_runtime_python", "version": "0.8"}], "package": {"include": ["manifest.json", "property.json", "BUILD.gn", "**.tent", "**.py", "README.md", "tests/**"]}, "api": {"property": {"api_key": {"type": "string"}, "voice": {"type": "string"}, "model": {"type": "string"}, "sample_rate": {"type": "int64"}}, "data_in": [{"name": "text_data", "property": {"text": {"type": "string"}}}], "cmd_in": [{"name": "flush"}], "cmd_out": [{"name": "flush"}], "audio_frame_out": [{"name": "pcm_frame"}]}}