@echo off
echo ========================================
echo 🚀 LOCAL VOICE AGENT ACTIVATION SCRIPT
echo ========================================
echo.
echo This script will help you activate your local voice agent!
echo.
echo ✅ Prerequisites Check:
echo   - Ollama: INSTALLED ✅ (qwen2.5vl:32b detected)
echo   - Python Dependencies: INSTALLED ✅
echo   - Configuration: READY ✅
echo.
echo ⚠️  Missing: Go Programming Language
echo.
echo 📋 NEXT STEPS:
echo.
echo 1. Install Go (choose one method):
echo    Method A: Download from https://golang.org/dl/
echo    Method B: Use winget: winget install golang.go
echo    Method C: Use chocolatey: choco install golang
echo.
echo 2. After installing Go, restart this terminal and run:
echo    cd ai_agents\agents
echo    go run main.go
echo.
echo 3. Open your browser to: http://localhost:3000
echo    Select: "🏠 Local Voice Agent - Fully Offline"
echo.
echo 🎯 Your local voice agent features:
echo   • Qwen 2.5VL (32B) - Vision + LLM
echo   • Faster Whisper - Speech-to-Text
echo   • Edge TTS - Natural Voice
echo   • 100%% Local & Private
echo.
echo Press any key to open Go download page...
pause > nul
start https://golang.org/dl/
echo.
echo After installing Go, run this script again!
pause