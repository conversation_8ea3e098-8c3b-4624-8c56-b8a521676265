# 🚀 Local Voice Agent - Fully Offline AI Assistant

A **high-performance, completely local voice agent** that combines vision, speech, and conversation capabilities. Everything runs on your machine for maximum privacy and speed.

## ⚡ Performance Features

- **Ultra-Fast STT**: Faster Whisper (5x faster than <PERSON><PERSON><PERSON> Whisper)
- **Natural Voice**: Edge TTS with human-like voices
- **Vision Enabled**: Qwen 2.5VL can see and understand images
- **Real-time Streaming**: Low-latency responses
- **Voice Activity Detection**: Intelligent speech detection
- **GPU Accelerated**: Auto-detects and uses CUDA when available

## 🧠 AI Models Used

### 📝 Speech-to-Text (STT)
- **Model**: Faster Whisper (Base)
- **Speed**: 5x faster than standard Whisper
- **Accuracy**: High accuracy with VAD filtering
- **Languages**: Multi-language support

### 🤖 Large Language Model (LLM)
- **Model**: Qwen 2.5VL (via Ollama)
- **Capabilities**: Vision + Text understanding
- **Memory**: Optimized conversation history
- **Streaming**: Real-time response generation

### 🗣️ Text-to-Speech (TTS)
- **Engine**: Microsoft Edge TTS
- **Quality**: Neural voices, very natural
- **Speed**: Fast synthesis with caching
- **Voices**: Multiple natural voice options

## 🏗️ Architecture

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────┐
│   Camera    │───▶│              │    │             │    │             │
│   & Mic     │    │  Agora RTC   │───▶│ Faster      │───▶│ Ollama      │
│  (Input)    │    │  (WebRTC)    │    │ Whisper STT │    │ Qwen 2.5VL  │
└─────────────┘    │              │    │             │    │             │
                   │              │◀───┤             │◀───┤             │
┌─────────────┐    │              │    │             │    │             │
│  Speaker    │◀───│              │    │ Edge TTS    │    │             │
│ (Output)    │    │              │    │             │    │             │
└─────────────┘    └──────────────┘    └─────────────┘    └─────────────┘
```

## 🚀 Quick Setup

### Prerequisites
- Python 3.8+
- Go (for TEN Framework)
- Ollama installed
- 4-8GB RAM recommended

### Automated Setup
```bash
# Run the setup script
python setup_local_voice_agent.py
```

### Manual Setup

1. **Install Python Dependencies**
```bash
pip install torch torchaudio transformers faster-whisper edge-tts ollama webrtcvad soundfile numpy pillow
```

2. **Setup Ollama**
```bash
# Install Ollama from https://ollama.ai
ollama pull qwen2.5-vl
ollama serve
```

3. **Start the Agent**
```bash
cd ai_agents/agents
go run main.go
```

4. **Open Web Interface**
- Go to: http://localhost:3000
- Select: "🏠 Local Voice Agent - Fully Offline"

## 🎛️ Configuration

### STT Configuration
```json
{
  "model_id": "base",        // tiny, base, small, medium, large
  "language": "en",          // Language code
  "vad_enabled": true,       // Voice activity detection
  "device": "auto"           // auto, cpu, cuda
}
```

### LLM Configuration
```json
{
  "model": "qwen2.5-vl",
  "base_url": "http://localhost:11434",
  "temperature": 0.8,        // Creativity level
  "stream_mode": true        // Real-time streaming
}
```

### TTS Configuration
```json
{
  "voice_preset": "female_natural",  // Voice type
  "speed": 1.1,                      // Speech speed
  "sample_rate": 24000               // Audio quality
}
```

## 🎯 Voice Presets

### Natural Voices Available:
- `female_natural` - en-US-AriaNeural (recommended)
- `male_natural` - en-US-DavisNeural
- `female_friendly` - en-US-JennyNeural
- `male_friendly` - en-US-GuyNeural
- `female_professional` - en-US-SaraNeural
- `male_professional` - en-US-TonyNeural

## ⚡ Performance Optimization

### Hardware Acceleration
- **GPU**: Automatically uses CUDA if available
- **CPU**: Optimized with int8 quantization
- **Memory**: Efficient buffering and caching

### Speed Optimizations
- **STT**: Faster Whisper with VAD filtering
- **LLM**: Streaming responses with optimized context
- **TTS**: Cached synthesis for common phrases
- **Audio**: Optimized chunk sizes for low latency

### Model Size Trade-offs
| Model Size | Speed | Accuracy | VRAM Usage |
|------------|-------|----------|------------|
| tiny       | Fastest | Good | ~1GB |
| base       | Fast | Better | ~2GB |
| small      | Medium | Great | ~3GB |
| medium     | Slow | Excellent | ~5GB |

## 🔧 Troubleshooting

### Common Issues

**1. Ollama Connection Failed**
```bash
# Start Ollama server
ollama serve

# Check if running
curl http://localhost:11434/api/version
```

**2. Audio Issues**
- Check microphone permissions
- Ensure correct sample rate (16kHz)
- Verify WebRTC audio access

**3. Model Loading Slow**
- Use smaller Whisper model (`tiny` or `base`)
- Enable GPU acceleration
- Increase system RAM

**4. Poor Voice Quality**
- Increase TTS sample rate to 24kHz
- Try different voice presets
- Check network for Edge TTS

### Performance Tuning

**For Speed (Lower Latency)**:
```json
{
  "stt": {"model_id": "tiny", "chunk_length_s": 1},
  "llm": {"max_tokens": 1024, "temperature": 0.7},
  "tts": {"speed": 1.2}
}
```

**For Quality (Higher Accuracy)**:
```json
{
  "stt": {"model_id": "base", "chunk_length_s": 3},
  "llm": {"max_tokens": 2048, "temperature": 0.8},
  "tts": {"sample_rate": 24000, "speed": 1.0}
}
```

## 🛡️ Privacy & Security

- **100% Local**: No data leaves your machine
- **No APIs**: No external API keys required
- **Real-time**: No cloud processing delays
- **Offline**: Works without internet (after setup)

## 📊 System Requirements

### Minimum
- CPU: 4 cores, 2.5GHz
- RAM: 4GB
- Storage: 5GB free space

### Recommended
- CPU: 8 cores, 3.0GHz+
- RAM: 8GB+
- GPU: NVIDIA GPU with 4GB+ VRAM
- Storage: 10GB free space

## 🔄 Updates

To update models:
```bash
# Update Qwen model
ollama pull qwen2.5-vl

# Update Python packages
pip install --upgrade faster-whisper edge-tts transformers
```

## 💡 Tips for Best Experience

1. **Speak Clearly**: The VAD system works best with clear speech
2. **Good Lighting**: Better camera input = better vision understanding
3. **Stable Network**: Edge TTS needs internet for voice synthesis
4. **Quiet Environment**: Reduces false STT triggers
5. **GPU**: Use NVIDIA GPU for 5-10x speed improvement

## 🤝 Contributing

Found issues or want to improve performance?
- Check the TEN Framework documentation
- Submit issues with performance metrics
- Share optimization discoveries

## 📜 License

This implementation uses:
- TEN Framework (Apache 2.0)
- Faster Whisper (MIT)
- Edge TTS (MIT)
- Ollama (MIT)

---

**🎉 Enjoy your fully local, private, and fast AI voice assistant!**