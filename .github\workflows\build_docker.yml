name: Build Docker Image

on:
  push:
    branches:
      - "**"
    paths:
      - "tools/docker_for_building/**"
      - ".github/workflows/build_docker.yml"

permissions:
  contents: read
  packages: write
  security-events: write

env:
  IMAGE_NAME_PREFIX: ten_building_

concurrency:
  group: build-docker-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  build:
    strategy:
      matrix:
        include:
          - image_name: "ubuntu2204"
            workdir: "tools/docker_for_building/ubuntu/22.04"
            platforms: linux/amd64,linux/arm64
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build & Publish to Github Container Registry
        uses: elgohr/Publish-Docker-Github-Action@v5
        with:
          name: ${{ github.repository_owner }}/${{ env.IMAGE_NAME_PREFIX }}${{ matrix.image_name }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          registry: ghcr.io
          workdir: ${{ matrix.workdir }}
          platforms: ${{ matrix.platforms }}
          snapshot: true
          tags: "${{ github.ref == 'refs/heads/main' && 'latest,' || '' }}"
          no_push: ${{ github.event_name == 'pull_request' }}
