{"_ten": {"predefined_graphs": [{"name": "voice_assistant_realtime", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "rtc", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "subscribe_audio_sample_rate": 24000}}, {"type": "extension", "name": "v2v", "addon": "stepfun_v2v_python", "extension_group": "llm", "property": {"api_key": "${env:STEPFUN_API_KEY}", "temperature": 0.9, "model": "step-1o-audio", "max_tokens": 2048, "voice": "lin<PERSON><PERSON><PERSON><PERSON>e", "server_vad": true, "dump": true, "max_history": 10, "base_uri": "wss://api.stepfun.com"}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "weatherapi_tool_python", "addon": "weatherapi_tool_python", "extension_group": "default", "property": {"api_key": "${env:WEATHERAPI_API_KEY|}"}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "v2v"}]}, {"name": "on_user_left", "dest": [{"extension": "v2v"}]}, {"name": "on_connection_failure", "dest": [{"extension": "v2v"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "v2v"}]}]}, {"extension": "v2v", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}, {"name": "tool_call", "dest": [{"extension": "weatherapi_tool_python"}]}], "data": [{"name": "text_data", "dest": [{"extension": "message_collector"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "weatherapi_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "v2v"}]}]}]}], "log_level": 3}}