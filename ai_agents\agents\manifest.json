{"type": "app", "name": "local_voice_agent", "version": "0.1.0", "dependencies": [{"type": "system", "name": "ten_runtime_go", "version": "0.6.4"}, {"type": "system", "name": "ten_runtime_python", "version": "0.6.4"}, {"type": "extension", "name": "agora_rtc", "version": "0.6.4"}, {"type": "extension", "name": "huggingface_stt_python", "version": "0.1.0"}, {"type": "extension", "name": "olla<PERSON>_qwen_python", "version": "0.1.0"}, {"type": "extension", "name": "huggingface_tts_python", "version": "0.1.0"}, {"type": "extension", "name": "interrupt_detector_python", "version": "0.1.0"}, {"type": "extension", "name": "message_collector", "version": "0.1.0"}]}