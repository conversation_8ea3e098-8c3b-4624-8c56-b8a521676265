{"type": "app", "name": "agent_experimental", "version": "0.8.0", "dependencies": [{"type": "system", "name": "ten_runtime_go", "version": "0.8"}, {"type": "extension", "name": "agora_rtc", "version": "=0.12.0"}, {"type": "extension", "name": "agora_sess_ctrl", "version": "=0.4.4"}, {"type": "system", "name": "azure_speech_sdk", "version": "1.38.0"}, {"type": "system", "name": "ten_ai_base", "version": "0.4.1"}, {"type": "extension", "name": "azure_tts", "version": "=0.8.1"}, {"type": "extension", "name": "agora_rtm", "version": "=0.8.1"}, {"type": "extension", "name": "interrupt_detector_python", "version": "=0.1.0"}, {"type": "extension", "name": "openai_chatgpt_python", "version": "=0.1.0"}, {"type": "extension", "name": "message_collector", "version": "=0.1.0"}, {"type": "extension", "name": "fashionai", "version": "=0.1.0"}, {"type": "extension", "name": "qwen_llm_python", "version": "=0.1.0"}, {"type": "extension", "name": "cosy_tts_python", "version": "=0.1.0"}, {"type": "extension", "name": "http_server_python", "version": "=0.10.2"}, {"type": "extension", "name": "aliyun_text_embedding", "version": "=0.1.0"}, {"type": "extension", "name": "aliyun_analyticdb_vector_storage", "version": "=0.1.0"}, {"type": "extension", "name": "file_chunker", "version": "=0.1.0"}, {"type": "extension", "name": "llama_index_chat_engine", "version": "=0.1.0"}, {"type": "extension", "name": "openai_v2v_python", "version": "=0.1.0"}, {"type": "extension", "name": "weatherapi_tool_python", "version": "=0.1.0"}, {"type": "extension", "name": "bingsearch_tool_python", "version": "=0.1.0"}, {"type": "extension", "name": "tsdb_firestore", "version": "=0.1.0"}, {"type": "extension", "name": "minimax_v2v_python", "version": "=0.1.0"}]}