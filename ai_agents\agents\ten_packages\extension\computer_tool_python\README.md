# computer_tool_python

This is the tool demo for computer use.

## Features

- Open the Application
- Analyze the code through screen sharing
- Generate code
- Save the content to the Note book

## API

Refer to `api` definition in [manifest.json] and default values in [property.json](property.json).

### Out:

- `tool_register`: auto register tool to llm

### In:

- `tool_call`: sync cmd to call computer usecase action