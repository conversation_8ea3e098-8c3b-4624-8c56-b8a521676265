{"type": "extension", "name": "aliyun_text_embedding", "version": "0.1.0", "dependencies": [{"type": "system", "name": "ten_runtime_python", "version": "0.8"}], "api": {"property": {"api_key": {"type": "string"}, "model": {"type": "string"}}, "cmd_in": [{"name": "embed", "property": {"input": {"type": "string"}}, "required": ["input"], "result": {"property": {"embedding": {"type": "array", "items": {"type": "float64"}}, "code": {"type": "string"}, "message": {"type": "string"}}}}, {"name": "embed_batch", "property": {"inputs": {"type": "array", "items": {"type": "string"}}}, "required": ["inputs"], "result": {"property": {"embeddings": {"type": "string"}, "code": {"type": "string"}, "message": {"type": "string"}}}}]}}